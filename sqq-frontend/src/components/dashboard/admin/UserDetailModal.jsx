import { User, Mail, Building, Shield, Server, Eye, Database, Key } from 'lucide-react';
import PropTypes from 'prop-types';
import { Modal } from '../../common';
import { useLanguage } from '../../../hooks';

/**
 * Modal hermoso para mostrar detalles completos del usuario
 * Diseño moderno hermoso minimalista y con consistencia
 */
const UserDetailModal = ({ isOpen, onClose, user, darkMode }) => {
  const { t } = useLanguage();

  if (!user) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-2 sm:gap-3">
          <User size={20} className="text-blue-500 sm:w-6 sm:h-6" />
          <div>
            <h3 className="text-lg sm:text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              {t('admin.users.detail.title')}
            </h3>
            <p className="text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('admin.users.detail.subtitle')}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-4xl"
      darkMode={darkMode}
    >
      <div className="space-y-3 sm:space-y-4">
        {/* Información Personal */}
        <div className={`p-3 sm:p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <h4 className="text-sm sm:text-base font-light tracking-wide mb-3 flex items-center gap-2 text-gray-900 dark:text-white">
            <User size={16} className="text-orange-500 sm:w-5 sm:h-5" />
            {t('admin.users.detail.personalInfo')}
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
            <div>
              <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.users.detail.fullName')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                {user.firstName} {user.lastName}
              </div>
            </div>

            <div>
              <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.users.detail.email')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                {user.email}
              </div>
            </div>

            <div>
              <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.users.detail.company')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                {user.company || t('admin.users.detail.notSpecified')}
              </div>
            </div>

            <div>
              <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.users.detail.role')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                {user.role === 'ADMIN' ? t('admin.users.detail.administrator') : t('admin.users.detail.user')}
              </div>
            </div>

            <div>
              <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.users.detail.status')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                user.isActive
                  ? darkMode
                    ? 'border-green-600 bg-green-900/20 text-green-400'
                    : 'border-green-300 bg-green-50 text-green-700'
                  : darkMode
                    ? 'border-red-600 bg-red-900/20 text-red-400'
                    : 'border-red-300 bg-red-50 text-red-700'
              }`}>
                {user.isActive ? t('admin.users.detail.active') : t('admin.users.detail.inactive')}
              </div>
            </div>
          </div>
        </div>

        {/* Configuración CTM */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <h4 className="text-base font-light tracking-wide mb-3 flex items-center gap-2 text-gray-900 dark:text-white">
            <Database size={18} className="text-blue-500" />
            {t('admin.users.detail.cipherTrustManager')}
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
            <div>
              <label className="block text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.users.detail.ipAddress')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                {user.ctmIpAddress || 'https://ctm.example.com:443'}
              </div>
            </div>

            <div>
              <label className="block text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.users.detail.username')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                {user.ctmUsername || '<EMAIL>'}
              </div>
            </div>

            <div>
              <label className="block text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.users.detail.password')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                ••••••••
              </div>
            </div>

            <div>
              <label className="block text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.users.detail.domain')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                {user.ctmDomain || 'root'}
              </div>
            </div>
          </div>
        </div>

        {/* Configuración SEQRNG */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <h4 className="text-base font-light tracking-wide mb-3 flex items-center gap-2 text-gray-900 dark:text-white">
            <Key size={18} className="text-green-500" />
            {t('admin.users.detail.seqrng')}
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
            <div>
              <label className="block text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.users.detail.ipAddress')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                {user.seqrngIpAddress || 'https://seqrng.example.com:1982'}
              </div>
            </div>

            <div>
              <label className="block text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.users.detail.apiToken')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                {user.seqrngApiToken || '1|cifovFzUWTeAPUeAYlZYefsY3tXFf9Z1TV5WV0YL'}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="flex justify-center mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
        <button
          onClick={onClose}
          className="px-8 py-3 rounded-xl bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-light tracking-wide shadow-sm hover:shadow-md transform hover:scale-105 transition-all duration-300"
        >
          {t('common.close')}
        </button>
      </div>
    </Modal>
  );
};

UserDetailModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  user: PropTypes.object,
  darkMode: PropTypes.bool.isRequired
};

export default UserDetailModal;
