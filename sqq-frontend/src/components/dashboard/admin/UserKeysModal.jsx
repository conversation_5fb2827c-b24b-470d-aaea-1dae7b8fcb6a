import { useState } from 'react';
import { Key, Eye, Calendar, Shield, AlertCircle } from 'lucide-react';
import PropTypes from 'prop-types';
import { Modal, LoadingSpinner } from '../../common';
import KeyDetailModal from './KeyDetailModal';
import { useLanguage } from '../../../hooks/useLanguage';

/**
 * Modal para mostrar las llaves de un usuario específico
 */
const UserKeysModal = ({
  isOpen,
  onClose,
  user,
  userKeys,
  isLoading,
  darkMode
}) => {
  const { t, currentLanguage } = useLanguage();
  const [selectedKey, setSelectedKey] = useState(null);
  const [showKeyDetail, setShowKeyDetail] = useState(false);
  const [hideKeysModal, setHideKeysModal] = useState(false); // Para ocultar temporalmente

  const handleViewKeyDetail = (key) => {
    setSelectedKey(key);
    // Ocultar este modal temporalmente y abrir el de detalle
    setHideKeysModal(true); // Oculta el modal de llaves sin cerrar
    setTimeout(() => {
      setShowKeyDetail(true); // Abre el modal de detalle
    }, 150);
  };

  const handleCloseKeyDetail = () => {
    setShowKeyDetail(false);
    setSelectedKey(null);
    // Volver a mostrar el modal de llaves
    setTimeout(() => {
      setHideKeysModal(false); // Vuelve a mostrar el modal de llaves
    }, 150);
  };

  // Reset cuando el modal se cierre completamente
  const handleModalClose = () => {
    setHideKeysModal(false);
    setSelectedKey(null);
    setShowKeyDetail(false);
    onClose();
  };

  const getStatusColor = (key) => {
    if (key.status === 'UPLOADED_TO_CTM' || key.uploadedToCtm === true) {
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    }
    if (key.status === 'FAILED' || key.isSuccessful === false) {
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    }
    return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
  };

  const getStatusText = (key) => {
    if (key.status === 'UPLOADED_TO_CTM') return t('admin.users.keys.statusActive');
    if (key.status === 'FAILED') return t('admin.users.keys.statusFailed');
    return key.status || t('admin.users.keys.statusPending');
  };

  const getStatusIcon = (key) => {
    if (key.status === 'UPLOADED_TO_CTM' || key.uploadedToCtm === true) {
      return <Shield size={18} className="text-green-600" />;
    }
    if (key.status === 'FAILED' || key.isSuccessful === false) {
      return <AlertCircle size={18} className="text-red-600" />;
    }
    return <Key size={18} className="text-yellow-600" />;
  };

  return (
    <>
      <Modal
        isOpen={isOpen && !hideKeysModal}
        onClose={handleModalClose}
        title={
          <div className="flex items-center gap-3">
            <Key size={20} className="text-orange-500" />
            <span>{user?.firstName} {user?.lastName} - {t('admin.users.keys.title')}</span>
          </div>
        }
        maxWidth="max-w-4xl"
        darkMode={darkMode}
      >
        {isLoading ? (
          <LoadingSpinner message={t('admin.users.keys.loading')} />
        ) : (
          <div>
            {userKeys && userKeys.length > 0 ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between mb-4">
                  <p className="text-gray-600 dark:text-gray-300">
                    {t('admin.users.keys.totalKeys')}: <span className="font-semibold">{userKeys.length}</span>
                  </p>
                  <div className="flex gap-4 text-sm">
                    <span className="flex items-center gap-1">
                      <Shield size={16} className="text-green-600" />
                      {t('admin.users.keys.active')}: {userKeys.filter(k => k.status === 'UPLOADED_TO_CTM' || k.uploadedToCtm).length}
                    </span>
                    <span className="flex items-center gap-1">
                      <AlertCircle size={16} className="text-red-600" />
                      {t('admin.users.keys.failed')}: {userKeys.filter(k => k.status === 'FAILED' || k.isSuccessful === false).length}
                    </span>
                  </div>
                </div>

                <div className="grid gap-3">
                  {userKeys.map((key) => (
                    <div
                      key={key.id}
                      className={`p-4 rounded-lg border ${
                        darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
                      }`}
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            {getStatusIcon(key)}
                            <h4 className="font-semibold text-lg">{key.name}</h4>
                            <span
                              className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(key)}`}
                            >
                              {getStatusText(key)}
                            </span>
                            <span className="px-2 py-1 rounded text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                              {key.type || key.algorithm}
                            </span>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div className="flex items-center gap-2">
                              <Calendar size={16} className="text-gray-500" />
                              <span className="text-gray-600 dark:text-gray-300">
                                {key.createdAt ? new Date(key.createdAt).toLocaleDateString(
                                  currentLanguage === 'es' ? 'es-ES' : 'en-US'
                                ) : 'N/A'}
                              </span>
                            </div>
                            <div>
                              <span className="text-gray-600 dark:text-gray-300">
                                {key.ctmKeyId ? 'CTM ID: ' : 'ID: '}
                              </span>
                              <span className="font-mono text-xs">
                                {(key.ctmKeyId || key.id).substring(0, 8)}...
                              </span>
                            </div>
                            <div>
                              <span className="text-gray-600 dark:text-gray-300">Bytes: </span>
                              <span className="font-medium">{key.numBytes || key.num_bytes || 'N/A'}</span>
                            </div>
                          </div>
                        </div>

                        <div className="ml-4">
                          <button
                            onClick={() => handleViewKeyDetail(key)}
                            className="p-2 border rounded-lg transition-all duration-200 transform border-gray-200 bg-gray-50 text-blue-500 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-blue-400 dark:hover:bg-blue-900/20 dark:hover:border-blue-500"
                            title={t('admin.users.keys.viewDetail')}
                          >
                            <Eye size={18} />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <Key size={48} className="mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Sin llaves registradas
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Este usuario aún no ha creado ninguna llave cuántica.
                </p>
              </div>
            )}

            {/* Footer con botón centrado elegante */}
            <div className="flex justify-center mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
              <button
                onClick={handleModalClose}
                className="px-8 py-3 rounded-xl bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-light tracking-wide shadow-sm hover:shadow-md transform hover:scale-105 transition-all duration-300"
              >
                {t('admin.users.keys.close')}
              </button>
            </div>
          </div>
        )}
      </Modal>

      {/* Modal de detalle de llave */}
      <KeyDetailModal
        isOpen={showKeyDetail}
        onClose={handleCloseKeyDetail}
        keyData={selectedKey}
        darkMode={darkMode}
        isAdmin={true}
      />
    </>
  );
};

UserKeysModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  user: PropTypes.object,
  userKeys: PropTypes.array,
  isLoading: PropTypes.bool,
  darkMode: PropTypes.bool.isRequired
};

export default UserKeysModal;
