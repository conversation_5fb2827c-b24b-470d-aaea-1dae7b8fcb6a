import React, { useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { Line } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, Filler } from 'chart.js';
import { TrendingUp } from 'lucide-react';
import { gsap } from 'gsap';
import { useLanguage } from '../../../hooks';

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, Filler);

const SimpleLineChart = ({ darkMode, keys = [] }) => {
  const containerRef = useRef(null);
  const chartRef = useRef(null);
  const { t } = useLanguage();

  // Procesar datos reales de las últimas 4 semanas
  const getWeeklyData = () => {
    if (!keys || !Array.isArray(keys) || keys.length === 0) {
      return {
        weeks: ['Semana 1', 'Semana 2', 'Semana 3', 'Semana 4'],
        weeklyData: [0, 0, 0, 0]
      };
    }

    const today = new Date();
    const weeks = [];
    const weeklyData = [0, 0, 0, 0];

    // Generar las últimas 4 semanas
    for (let i = 3; i >= 0; i--) {
      const weekStart = new Date(today);
      weekStart.setDate(today.getDate() - (i * 7));
      weekStart.setHours(0, 0, 0, 0);
      
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);
      weekEnd.setHours(23, 59, 59, 999);
      
      weeks.push(`Semana ${4 - i}`);
      
      // Contar llaves generadas en esta semana
      const weekKeys = keys.filter(key => {
        if (!key.createdAt) return false;
        const keyDate = new Date(key.createdAt);
        return keyDate >= weekStart && keyDate <= weekEnd;
      });
      
      weeklyData[3 - i] = weekKeys.length;
    }

    return { weeks, weeklyData };
  };

  const { weeks, weeklyData } = getWeeklyData();

  // Animación GSAP para línea progresiva y puntos stagger
  useEffect(() => {
    if (containerRef.current && chartRef.current) {
      // Configurar estado inicial del contenedor
      gsap.set(containerRef.current, {
        opacity: 0,
        y: 30,
        scale: 0.95
      });

      // Configurar estado inicial del área del gráfico
      gsap.set(chartRef.current, {
        opacity: 0
      });

      // Timeline de animación
      const tl = gsap.timeline();
      
      // 1. Entrada del contenedor
      tl.to(containerRef.current, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.6,
        ease: "back.out(1.1)"
      })
      // 2. Aparición del área del gráfico
      .to(chartRef.current, {
        opacity: 1,
        duration: 0.4,
        ease: "power2.out"
      }, "-=0.2")
      // 3. Simular animación de línea progresiva con clip-path
      .fromTo(chartRef.current.querySelector('canvas'), 
        {
          clipPath: 'inset(0 100% 0 0)'
        },
        {
          clipPath: 'inset(0 0% 0 0)',
          duration: 1.5,
          ease: "power2.out"
        }, "-=0.1"
      );

      // Cleanup
      return () => {
        tl.kill();
      };
    }
  }, []); // Solo al montar el componente

  // Crear gradiente para el área bajo la línea
  const createGradient = (ctx) => {
    const gradient = ctx.createLinearGradient(0, 0, 0, 400);
    if (darkMode) {
      gradient.addColorStop(0, 'rgba(59, 130, 246, 0.3)');
      gradient.addColorStop(1, 'rgba(59, 130, 246, 0.05)');
    } else {
      gradient.addColorStop(0, 'rgba(59, 130, 246, 0.2)');
      gradient.addColorStop(1, 'rgba(59, 130, 246, 0.02)');
    }
    return gradient;
  };

  const data = {
    labels: weeks,
    datasets: [
      {
        label: t('dashboard.charts.keysGenerated.title'),
        data: weeklyData,
        borderColor: darkMode ? '#3b82f6' : '#2563eb',
        backgroundColor: (context) => {
          const { ctx } = context.chart;
          return createGradient(ctx);
        },
        borderWidth: 3,
        pointBackgroundColor: darkMode ? '#3b82f6' : '#2563eb',
        pointBorderColor: darkMode ? '#1e40af' : '#1d4ed8',
        pointBorderWidth: 2,
        pointRadius: 6,
        pointHoverRadius: 8,
        fill: true,
        tension: 0.4, // Línea curva suave
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: { display: false },
      tooltip: {
        backgroundColor: darkMode ? '#1E293B' : '#fff',
        titleColor: darkMode ? '#fff' : '#1E293B',
        bodyColor: darkMode ? '#F1F5F9' : '#1E293B',
        borderColor: darkMode ? '#334155' : '#E5E7EB',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: false,
        callbacks: {
          label: function(context) {
            return `${context.raw} ${t('dashboard.charts.weeklyEvolution.keysGenerated')}`;
          }
        }
      },
    },
    scales: {
      x: {
        grid: { 
          color: darkMode ? 'rgba(51,65,85,0.3)' : '#E5E7EB',
          drawBorder: false
        },
        ticks: { 
          color: darkMode ? '#CBD5E1' : '#374151',
          font: { weight: 'bold', size: 11 }
        },
      },
      y: {
        beginAtZero: true,
        grid: { 
          color: darkMode ? 'rgba(51,65,85,0.3)' : '#E5E7EB',
          drawBorder: false
        },
        ticks: { 
          color: darkMode ? '#CBD5E1' : '#374151',
          font: { weight: 'bold', size: 11 },
          stepSize: Math.ceil(Math.max(...weeklyData, 5) / 5) // Ajustar step size dinámicamente
        },
      },
    },
    animation: false, // Deshabilitamos la animación de Chart.js para usar GSAP
    maintainAspectRatio: false,
  };

  return (
    <div 
      ref={containerRef}
      className={`p-6 rounded-2xl shadow-xl transition-all duration-300 hover:shadow-2xl hover:scale-[1.02] group ${
        darkMode ? 'bg-gray-800 border border-gray-700 hover:bg-gray-700' : 'bg-white border border-gray-200 hover:bg-gray-50'
      }`}
    >
      <div className="flex items-center gap-3 mb-6">
        <div className={`p-3 rounded-xl transition-all duration-300 group-hover:scale-110 ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
          <TrendingUp className="text-blue-500 transition-colors duration-300 group-hover:text-blue-400" size={24} />
        </div>
        <div>
          <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            {t('dashboard.charts.weeklyEvolution.title')}
          </h3>
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('dashboard.charts.weeklyEvolution.subtitle')}
          </p>
        </div>
      </div>
      
      <div ref={chartRef} className="h-64">
        <Line data={data} options={options} />
      </div>
      
      {/* Estadísticas resumidas */}
      <div className="grid grid-cols-2 gap-4 mt-4">
        <div className="text-center transition-all duration-300 group-hover:scale-105">
          <span className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('dashboard.charts.weeklyEvolution.totalGenerated')}
          </span>
          <p className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            {weeklyData.reduce((a, b) => a + b, 0)}
          </p>
        </div>
        <div className="text-center transition-all duration-300 group-hover:scale-105">
          <span className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('dashboard.charts.weeklyEvolution.weeklyAverage')}
          </span>
          <p className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            {Math.round(weeklyData.reduce((a, b) => a + b, 0) / weeklyData.length)}
          </p>
        </div>
      </div>
    </div>
  );
};

SimpleLineChart.propTypes = {
  darkMode: PropTypes.bool.isRequired,
  keys: PropTypes.array,
};

export default SimpleLineChart;