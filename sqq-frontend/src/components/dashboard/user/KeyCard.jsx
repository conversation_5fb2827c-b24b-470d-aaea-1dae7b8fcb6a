import { Trash2, <PERSON>, <PERSON>f<PERSON><PERSON><PERSON>, History } from 'lucide-react';
import PropTypes from 'prop-types';
import { Button } from '../../common';
import { useLanguage } from '../../../hooks';

/**
 * Componente para mostrar información de una llave en formato de tarjeta
 */
const KeyCard = ({ keyData, darkMode, onDelete, onViewDetail, onUpdate, onViewVersions, disabled, versionInfo }) => {
  const { t, currentLanguage } = useLanguage();
  
  const getStatusInfo = (key) => {
    if (key.status === 'UPLOADED_TO_CTM' || key.uploadedToCtm === true) {
      return {
        text: 'uploaded_to_ctm',
        displayText: t('keys.status.active'),
        className: 'border border-green-200 bg-green-50 text-green-700 dark:border-green-700 dark:bg-green-900/20 dark:text-green-300'
      };
    }
    if (key.status === 'FAILED' || key.isSuccessful === false) {
      return {
        text: 'failed',
        displayText: t('keys.status.failed'),
        className: 'border border-red-200 bg-red-50 text-red-700 dark:border-red-700 dark:bg-red-900/20 dark:text-red-300'
      };
    }
    return {
      text: 'pending',
      displayText: t('keys.status.pending'),
      className: 'border border-orange-200 bg-orange-50 text-orange-700 dark:border-orange-700 dark:bg-orange-900/20 dark:text-orange-300'
    };
  };

  const getTypeInfo = (key) => {
    // Priorizar algorithm sobre type, ya que type puede contener 'hex_key'
    const type = key.algorithm || key.type || 'unknown';
    return {
      text: type,
      className: 'border border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-700 dark:bg-blue-900/20 dark:text-blue-300'
    };
  };

  // Función para obtener la información de versión activa
  const getVersionInfo = () => {
    // Si está cargando o no hay información, no mostrar etiqueta
    if (!versionInfo || versionInfo.loading) {
      return null;
    }

    // Solo mostrar si hay más de una versión
    if (!versionInfo.totalVersions || versionInfo.totalVersions <= 1) {
      return null;
    }

    // Buscar la versión activa (la más reciente)
    const activeVersion = versionInfo.versions?.find(version => 
      version.status === 'uploaded_to_ctm' || version.uploadedToCtm === true
    );

    if (!activeVersion) {
      return null;
    }

    // Obtener el número de versión basado en la posición en la lista ordenada
    const sortedVersions = [...(versionInfo.versions || [])].sort((a, b) => {
      const dateA = new Date(a.createdAt || a.updatedAt || 0);
      const dateB = new Date(b.createdAt || b.updatedAt || 0);
      return dateB - dateA;
    });

    const versionNumber = sortedVersions.findIndex(v => v.id === activeVersion.id) + 1;

    return {
      text: `${t('keys.versions.version')} ${versionNumber}`,
      className: 'border border-yellow-200 bg-yellow-500 text-black dark:border-yellow-600 dark:bg-yellow-500 dark:text-black'
    };
  };

  const statusInfo = getStatusInfo(keyData);
  const typeInfo = getTypeInfo(keyData);
  const versionInfoData = getVersionInfo();

  return (
    <div
      className={`p-4 sm:p-6 rounded-2xl border-2 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 ${
        darkMode
          ? 'bg-gray-800 border-gray-700 hover:border-gray-600'
          : 'bg-gradient-to-br from-white to-gray-50 border-gray-200 hover:border-gray-300'
      }`}
    >
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4">
        <div className="flex-1">
          {/* Header con nombre y etiquetas */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-3 mb-4">
            <h3 className="text-lg sm:text-xl font-light tracking-wide text-gray-900 dark:text-white">
              {keyData.name}
            </h3>

            <div className="flex flex-wrap gap-2">
              {/* Etiqueta de estado limpia y elegante */}
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${statusInfo.className}`}>
                {statusInfo.displayText}
              </span>

              {/* Etiqueta de tipo limpia y elegante */}
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${typeInfo.className}`}>
                {typeInfo.text}
              </span>

              {/* Etiqueta de versión activa (solo si hay múltiples versiones) */}
              {versionInfoData && (
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${versionInfoData.className}`}>
                  {versionInfoData.text}
                </span>
              )}
            </div>
          </div>

          {/* ID de la llave con diseño elegante */}
          <div className="mb-4">
            <div className={`p-3 sm:p-4 rounded-xl border-2 ${
              darkMode
                ? 'bg-gray-900/50 border-gray-600'
                : 'bg-gray-50 border-gray-200'
            }`}>
              <p className="font-mono text-xs sm:text-sm text-gray-700 dark:text-gray-300 break-all">
                {keyData.ctmKeyId || keyData.id}
              </p>
              <p className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 mt-1">
                {keyData.ctmKeyId ? t('keys.details.ctmKeyId') : t('keys.details.localId')}
              </p>
            </div>
          </div>

          {/* Información en grid elegante */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
            <div className={`p-3 rounded-xl ${
              darkMode ? 'bg-gray-900/30' : 'bg-white/70'
            }`}>
              <span className="text-xs font-light tracking-wide text-gray-600 dark:text-gray-400">{t('keys.details.created')}:</span>
              <p className="font-light tracking-wide text-gray-900 dark:text-white text-sm sm:text-base">
                {keyData.createdAt ? new Date(keyData.createdAt).toLocaleDateString(
                  currentLanguage === 'es' ? 'es-ES' : 'en-US'
                ) : 'N/A'}
              </p>
            </div>
            <div className={`p-3 rounded-xl ${
              darkMode ? 'bg-gray-900/30' : 'bg-white/70'
            }`}>
              <span className="text-xs font-light tracking-wide text-gray-600 dark:text-gray-400">{t('keys.details.algorithm')}:</span>
              <p className="font-light tracking-wide text-gray-900 dark:text-white text-sm sm:text-base">
                {keyData.algorithm || keyData.type || 'N/A'}
              </p>
            </div>
          </div>
        </div>

        {/* Botones de acción limpios */}
        <div className="flex flex-row gap-3 lg:ml-6 justify-center lg:justify-start">
          <button
            onClick={() => onViewDetail(keyData)}
            disabled={disabled}
            className={`p-2 border rounded-lg transition-all duration-200 transform ${
              disabled
                ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                : 'border-gray-200 bg-gray-50 text-blue-500 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-blue-400 dark:hover:bg-blue-900/20 dark:hover:border-blue-500'
            }`}
            title="Ver detalle de la llave"
          >
            <Eye size={18} />
          </button>
          <button
            onClick={onUpdate}
            disabled={disabled}
            className={`p-2 border rounded-lg transition-all duration-200 transform ${
              disabled
                ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                : 'border-gray-200 bg-gray-50 text-yellow-500 hover:bg-yellow-50 hover:border-yellow-300 hover:text-yellow-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-yellow-400 dark:hover:bg-yellow-900/20 dark:hover:border-yellow-500'
            }`}
            title="Actualizar llave"
          >
            <RefreshCw size={18} />
          </button>
          <button
            onClick={onViewVersions}
            disabled={disabled}
            className={`p-2 border rounded-lg transition-all duration-200 transform ${
              disabled
                ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                : 'border-gray-200 bg-gray-50 text-purple-500 hover:bg-purple-50 hover:border-purple-300 hover:text-purple-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-purple-400 dark:hover:bg-purple-900/20 dark:hover:border-purple-500'
            }`}
            title="Ver versiones de la llave"
          >
            <History size={18} />
          </button>
          <button
            onClick={onDelete}
            disabled={disabled}
            className={`p-2 border rounded-lg transition-all duration-200 transform ${
              disabled
                ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                : 'border-gray-200 bg-gray-50 text-red-500 hover:bg-red-50 hover:border-red-300 hover:text-red-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-red-400 dark:hover:bg-red-900/20 dark:hover:border-red-500'
            }`}
            title="Eliminar llave"
          >
            <Trash2 size={18} />
          </button>
        </div>
      </div>
    </div>
  );
};

KeyCard.propTypes = {
  keyData: PropTypes.object.isRequired,
  darkMode: PropTypes.bool.isRequired,
  onDelete: PropTypes.func.isRequired,
  onViewDetail: PropTypes.func.isRequired,
  onUpdate: PropTypes.func.isRequired,
  onViewVersions: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  versionInfo: PropTypes.object
};

export default KeyCard;
