import React, { useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { Bar } from 'react-chartjs-2';
import { Chart as ChartJS, BarElement, CategoryScale, LinearScale, Tooltip, Legend } from 'chart.js';
import { BarChart3 } from 'lucide-react';
import { gsap } from 'gsap';
import { useLanguage } from '../../../hooks';

ChartJS.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend);

const SimpleBarChart = ({ darkMode, keys = [] }) => {
  const containerRef = useRef(null);
  const chartRef = useRef(null);
  const { t, currentLanguage } = useLanguage();

  // Procesar datos reales de los últimos 7 días de forma simple
  const getLast7DaysData = () => {
    const keysCount = [0, 0, 0, 0, 0, 0, 0];
    const dayLabels = [];
    
    const today = new Date();
    
    // Generar fechas de los últimos 7 días y sus etiquetas reales
    const last7Days = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      date.setHours(0, 0, 0, 0); // Normalizar a medianoche
      last7Days.push(date);
      
      // Generar etiqueta del día real
      const dayName = date.toLocaleDateString(currentLanguage === 'es' ? 'es-ES' : 'en-US', { weekday: 'short' });
      const formattedDay = dayName.charAt(0).toUpperCase() + dayName.slice(1, 3);
      dayLabels.push(formattedDay);
    }

    // Si no hay llaves, retornar datos vacíos con días reales
    if (!keys || !Array.isArray(keys) || keys.length === 0) {
      return { days: dayLabels, keysCount };
    }

    // Contar llaves por día de forma segura
    keys.forEach(key => {
      if (key && key.createdAt) {
        try {
          const keyDate = new Date(key.createdAt);
          keyDate.setHours(0, 0, 0, 0); // Normalizar a medianoche
          
          if (!isNaN(keyDate.getTime())) {
            last7Days.forEach((day, index) => {
              if (keyDate.getTime() === day.getTime()) {
                keysCount[index]++;
              }
            });
          }
        } catch (error) {
          // Ignorar errores de fecha
        }
      }
    });

    return { days: dayLabels, keysCount };
  };

  const { days, keysCount } = getLast7DaysData();

  // Animación GSAP de entrada elegante (solo al montar)
  useEffect(() => {
    if (containerRef.current && chartRef.current) {
      // Configurar estado inicial
      gsap.set(containerRef.current, {
        opacity: 0,
        y: 30,
        scale: 0.95
      });

      gsap.set(chartRef.current, {
        opacity: 0,
        scale: 0.8
      });

      // Animación de entrada elegante con timeline
      const tl = gsap.timeline();
      
      tl.to(containerRef.current, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.7,
        ease: "back.out(1.2)"
      })
      .to(chartRef.current, {
        opacity: 1,
        scale: 1,
        duration: 0.8,
        ease: "power2.out"
      }, "-=0.5"); // Superponer las animaciones

      // Cleanup
      return () => {
        tl.kill();
      };
    }
  }, []); // Solo al montar el componente

  const data = {
    labels: days,
    datasets: [
      {
        label: t('dashboard.charts.keysGenerated.title'),
        data: keysCount,
        backgroundColor: darkMode ? 'rgba(59, 130, 246, 0.7)' : 'rgba(59, 130, 246, 0.6)',
        borderRadius: 8,
        barPercentage: 0.6,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: { display: false },
      tooltip: {
        backgroundColor: darkMode ? '#1E293B' : '#fff',
        titleColor: darkMode ? '#fff' : '#1E293B',
        bodyColor: darkMode ? '#F1F5F9' : '#1E293B',
        borderColor: darkMode ? '#334155' : '#E5E7EB',
        borderWidth: 1,
        cornerRadius: 8,
      },
    },
    scales: {
      x: {
        grid: { color: darkMode ? 'rgba(51,65,85,0.3)' : '#E5E7EB' },
        ticks: { color: darkMode ? '#CBD5E1' : '#334155' },
      },
      y: {
        beginAtZero: true,
        grid: { color: darkMode ? 'rgba(51,65,85,0.3)' : '#E5E7EB' },
        ticks: { color: darkMode ? '#CBD5E1' : '#334155', stepSize: 2 },
      },
    },
    animation: false,
  };

  return (
    <div 
      ref={containerRef}
      className={`p-6 rounded-2xl shadow-xl transition-all duration-300 hover:shadow-2xl hover:scale-[1.02] group ${
        darkMode ? 'bg-gray-800 border border-gray-700 hover:bg-gray-700' : 'bg-white border border-gray-200 hover:bg-gray-50'
      }`}
    >
      <div className="flex items-center gap-3 mb-6">
        <div className={`p-3 rounded-xl transition-all duration-300 group-hover:scale-110 ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
          <BarChart3 className="text-blue-500 transition-colors duration-300 group-hover:text-blue-400" size={24} />
        </div>
        <div>
          <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            {t('dashboard.charts.keysGenerated.title')}
          </h3>
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('dashboard.charts.keysGenerated.subtitle')}
          </p>
        </div>
      </div>
      <div ref={chartRef} className="h-64">
        <Bar data={data} options={options} />
      </div>
    </div>
  );
};

SimpleBarChart.propTypes = {
  darkMode: PropTypes.bool.isRequired,
  keys: PropTypes.array,
};

export default SimpleBarChart;