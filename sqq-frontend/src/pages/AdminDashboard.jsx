/**
 * @fileoverview Dashboard principal de administrador del sistema SQQ
 * @description Componente que proporciona una interfaz completa para la gestión
 * de usuarios, llaves cuánticas y estadísticas del sistema. Incluye navegación
 * por secciones, gestión de estado y integración con múltiples hooks.
 * <AUTHOR>
 * @version 2.0.0
 */

import { useState, useEffect } from "react";
import {
  Users,
  KeyRound,
  BarChart3,
  Activity,
  User,
} from "lucide-react";
import { useAuth, useUsers, useLanguage } from '../hooks/index';
import { useAdminStats } from '../hooks/useAdminStats';
import { DashboardLayout, DashboardStats } from '../components/dashboard';
import authService from '../services/auth/authService';
import { UserManagement, AdminKeysSection, AdminProfileManagement } from '../components/dashboard/admin';
import SuccessModal from '../components/common/SuccessModal';

/**
 * Componente principal del dashboard de administrador
 * @component
 * @description Proporciona una interfaz completa para administradores que incluye:
 * - Gestión de usuarios (CRUD completo)
 * - Visualización de estadísticas del sistema
 * - Gestión de llaves cuánticas
 * - Navegación por secciones con estado persistente
 * - Integración con sistema de autenticación y permisos
 *
 * @returns {JSX.Element} Dashboard de administrador con layout responsivo y navegación
 *
 * @example
 * // Uso básico del componente
 * <AdminDashboard />
 *
 * @requires useAuth - Hook para autenticación y datos del usuario actual
 * @requires useUsers - Hook para gestión de usuarios
 * @requires useLanguage - Hook para internacionalización
 * @requires useAdminStats - Hook para estadísticas del sistema
 */
const AdminDashboard = () => {
  /**
   * Estado local para controlar la sección activa del dashboard
   * @type {string} Sección actualmente visible ('dashboard', 'users', 'keys', 'profile')
   */
  const [activeSection, setActiveSection] = useState('dashboard');

  /**
   * Estado para controlar el modal de éxito
   * @type {boolean} Controla la visibilidad del modal de éxito
   */
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  /**
   * Estado para capturar el modo oscuro del DashboardLayout
   * @type {boolean} Estado del modo oscuro actual
   */
  const [currentDarkMode, setCurrentDarkMode] = useState(false);

  /**
   * Hook de autenticación - Proporciona datos del usuario actual y función de logout
   * @type {Object}
   * @property {Object} currentUser - Datos del usuario administrador autenticado
   * @property {Function} logout - Función para cerrar sesión de forma segura
   */
  const { user: currentUser, logout } = useAuth();

  /**
   * Hook de internacionalización - Proporciona función de traducción
   * @type {Object}
   * @property {Function} t - Función para obtener traducciones según idioma actual
   */
  const { t } = useLanguage();

  /**
   * Hook de gestión de usuarios - Proporciona CRUD completo y estado de carga
   * @type {Object}
   * @property {Array} users - Lista de todos los usuarios del sistema
   * @property {boolean} usersLoading - Estado de carga de operaciones de usuarios
   * @property {string|null} usersError - Error actual en operaciones de usuarios
   * @property {Function} getAllUsers - Función para cargar todos los usuarios
   * @property {Function} createUser - Función para crear nuevo usuario
   * @property {Function} updateUser - Función para actualizar usuario existente
   * @property {Function} deleteUser - Función para eliminar usuario
   * @property {Function} clearError - Función para limpiar errores de usuarios
   */
  const {
    users,
    isLoading: usersLoading,
    error: usersError,
    getAllUsers,
    createUser,
    updateUser,
    deleteUser,
    clearError
  } = useUsers();

  /**
   * Hook de estadísticas de administrador - Proporciona métricas del sistema
   * @type {Object}
   * @property {Object} stats - Estadísticas generales del sistema
   * @property {Array} usersWithKeys - Usuarios con información de llaves asociadas
   * @property {boolean} statsLoading - Estado de carga de estadísticas
   * @property {string|null} statsError - Error actual en carga de estadísticas
   * @property {Function} loadStats - Función para cargar estadísticas
   * @property {Function} clearStatsError - Función para limpiar errores de estadísticas
   */
  const {
    stats,
    usersWithKeys,
    isLoading: statsLoading,
    error: statsError,
    loadStats,
    clearError: clearStatsError
  } = useAdminStats();

  /**
   * Efecto para cargar datos iniciales del dashboard
   * @description Carga usuarios y estadísticas en paralelo al montar el componente
   * para optimizar el tiempo de carga inicial. Solo ejecuta si el usuario actual
   * tiene permisos de administrador.
   *
   * @dependencies currentUser, getAllUsers, loadStats - Funciones de carga de datos
   */
  useEffect(() => {
    /**
     * Función asíncrona para cargar datos iniciales
     * @async
     * @function loadData
     * @description Ejecuta carga paralela de usuarios y estadísticas
     * @throws {Error} Los errores se capturan y manejan en los hooks individuales
     */
    const loadData = async () => {
      try {
        // Cargar usuarios y estadísticas en paralelo para optimizar rendimiento
        await Promise.all([
          getAllUsers(),
          loadStats()
        ]);
      } catch {
        // Los errores se manejan automáticamente en los hooks individuales
        // Cada hook actualiza su estado de error correspondiente
      }
    };

    // Verificar permisos de administrador antes de cargar datos
    if (currentUser && (currentUser.role === 'ADMIN' || currentUser.role === 'admin')) {
      loadData();
    }
  }, [currentUser, getAllUsers, loadStats]);

  /**
   * Manejador para cerrar sesión de forma segura
   * @function handleLogout
   * @description Ejecuta el logout del usuario actual y limpia el estado de autenticación
   */
  const handleLogout = () => {
    logout();
  };

  /**
   * Manejador para actualizar datos de un usuario existente
   * @async
   * @function handleUpdateUser
   * @param {string} userId - ID único del usuario a actualizar
   * @param {Object} userData - Datos actualizados del usuario
   * @description Actualiza un usuario y refresca los datos del dashboard
   * @throws {Error} Error si falla la actualización del usuario
   */
  const handleUpdateUser = async (userId, userData) => {
    await updateUser(userId, userData);
    // Refrescar datos después de actualizar para mantener consistencia
    await Promise.all([getAllUsers(), loadStats()]);
  };

  /**
   * Manejador para crear un nuevo usuario
   * @async
   * @function handleCreateUser
   * @param {Object} userData - Datos del nuevo usuario a crear
   * @description Crea un nuevo usuario y refresca los datos del dashboard
   * @throws {Error} Error si falla la creación del usuario
   */
  const handleCreateUser = async (userData) => {
    await createUser(userData);
    // Refrescar datos después de crear para mostrar el nuevo usuario
    await Promise.all([getAllUsers(), loadStats()]);
  };

  /**
   * Manejador para eliminar un usuario del sistema
   * @async
   * @function handleDeleteUser
   * @param {string} userId - ID único del usuario a eliminar
   * @description Elimina un usuario y actualiza las estadísticas del sistema.
   * Incluye manejo de errores y notificaciones al usuario.
   * @throws {Error} Error si falla la eliminación del usuario
   * @todo Reemplazar alerts con sistema de notificaciones elegante
   */
  const handleDeleteUser = async (userId) => {
    try {
      // Usar el hook deleteUser que ya maneja la actualización de la lista
      await deleteUser(userId);

      // Refrescar estadísticas después de eliminar para mantener consistencia
      await loadStats();

      // TODO: Mostrar notificación elegante de éxito (reemplazar alert)
      alert('Usuario eliminado exitosamente');

    } catch (error) {
      console.error('Error eliminando usuario:', error);
      // TODO: Mostrar notificación elegante de error (reemplazar alert)
      alert('Error al eliminar el usuario: ' + error.message);
    }
  };

  /**
   * Manejador para actualizar el perfil del administrador actual
   * @async
   * @function handleUpdateProfile
   * @param {Object} profileData - Datos actualizados del perfil
   * @param {string} profileData.firstName - Nombre del administrador
   * @param {string} profileData.lastName - Apellido del administrador
   * @param {string} profileData.email - Email del administrador
   * @param {string} profileData.company - Empresa del administrador
   * @description Actualiza los datos del perfil del administrador actual.
   * Los cambios se reflejan automáticamente en la UI sin necesidad de recargar.
   * @throws {Error} Error si falla la actualización del perfil
   */
  const handleUpdateProfile = async (profileData) => {
    await updateUser(currentUser.id, profileData);
    // Los cambios se reflejan automáticamente sin necesidad de recargar
    // gracias al sistema de estado reactivo de los hooks
  };

  /**
   * Manejador para cambiar la contraseña del administrador
   * @async
   * @function handleChangePassword
   * @param {Object} passwordData - Datos para el cambio de contraseña
   * @param {string} passwordData.currentPassword - Contraseña actual
   * @param {string} passwordData.newPassword - Nueva contraseña
   * @param {string} passwordData.confirmPassword - Confirmación de nueva contraseña
   * @description Cambia la contraseña del administrador actual con validación.
   * @throws {Error} Error si falla el cambio de contraseña
   */
  const handleChangePassword = async (passwordData) => {
    try {
      // Llamada real al servicio de cambio de contraseña
      await authService.changePassword(
        passwordData.currentPassword,
        passwordData.newPassword,
        passwordData.confirmPassword
      );

      // Mostrar modal de éxito elegante
      setShowSuccessModal(true);
    } catch (error) {
      console.error('Error al cambiar contraseña:', error);
      // Lanzar el error tal como viene del servidor para que el modal lo maneje
      throw error;
    }
  };

  /**
   * Configuración de elementos de navegación del dashboard
   * @type {Array<Object>} Array de objetos que definen cada sección navegable
   * @description Define la estructura de navegación con iconos, etiquetas y claves únicas.
   * Cada elemento se traduce automáticamente según el idioma actual del usuario.
   *
   * @property {string} key - Identificador único de la sección para el estado
   * @property {string} label - Etiqueta traducida para mostrar en la UI
   * @property {Component} icon - Componente de icono de Lucide React
   */
  const navigationItems = [
    { key: 'dashboard', label: t('navigation.dashboard'), icon: BarChart3 },
    { key: 'users', label: t('admin.navigation.users'), icon: Users },
    { key: 'keys', label: t('admin.navigation.keys'), icon: KeyRound },
    { key: 'profile', label: t('navigation.profile'), icon: User }
  ];

  /**
   * Configuración de estadísticas principales del dashboard
   * @type {Array<Object>} Array de objetos que definen las métricas principales
   * @description Define las estadísticas clave del sistema con configuración visual
   * y cálculos de porcentajes para barras de progreso animadas.
   *
   * @property {Component} icon - Icono representativo de la métrica
   * @property {string} title - Título traducido de la estadística
   * @property {number} value - Valor numérico actual de la métrica
   * @property {string} description - Descripción traducida de la métrica
   * @property {string} iconColor - Clase CSS para el color del icono
   * @property {string} valueColor - Clase CSS para el color del valor
   * @property {number} percentage - Porcentaje calculado para barra de progreso (0-100)
   * @property {number} delay - Delay en segundos para animación escalonada
   */
  const mainStats = [
    {
      icon: Users,
      title: t('admin.stats.totalUsers'),
      value: stats.totalUsers,
      description: t('admin.stats.totalUsersDesc'),
      iconColor: "text-blue-500",
      valueColor: "text-blue-600 dark:text-blue-400",
      percentage: Math.min((stats.totalUsers / 100) * 100, 100), // Porcentaje basado en objetivo de 100 usuarios
      delay: 0
    },
    {
      icon: KeyRound,
      title: t('admin.stats.activeKeys'),
      value: stats.activeKeys,
      description: t('admin.stats.activeKeysDesc'),
      iconColor: "text-green-500",
      valueColor: "text-green-600 dark:text-green-400",
      percentage: Math.min((stats.activeKeys / 50) * 100, 100), // Porcentaje basado en objetivo de 50 llaves
      delay: 1
    },
    {
      icon: Activity,
      title: t('admin.stats.activeUsers'),
      value: stats.activeUsers,
      description: t('admin.stats.activeUsersDesc'),
      iconColor: "text-purple-500",
      valueColor: "text-purple-600 dark:text-purple-400",
      percentage: Math.min((stats.activeUsers / stats.totalUsers) * 100, 100), // Porcentaje de usuarios activos del total
      delay: 2
    }
  ];

  /**
   * Configuración de estadísticas adicionales (mini stats)
   * @type {Array<Object>} Array de métricas secundarias para el dashboard
   * @description Define estadísticas complementarias con menor prominencia visual
   * pero información importante para el administrador.
   *
   * @property {number} value - Valor numérico de la estadística
   * @property {string} label - Etiqueta traducida de la métrica
   * @property {string} valueColor - Clase CSS para el color del valor
   */
  const miniStats = [
    {
      value: stats.totalKeys,
      label: t('admin.stats.totalKeys'),
      valueColor: "text-gray-900 dark:text-white"
    },
    {
      value: stats.successfulKeys,
      label: t('admin.stats.successfulKeys'),
      valueColor: "text-green-600 dark:text-green-400"
    },
    {
      value: stats.failedKeys,
      label: t('admin.stats.failedKeys'),
      valueColor: "text-red-600 dark:text-red-400"
    },
    {
      value: stats.uploadedToCtm,
      label: t('admin.stats.inCTM'),
      valueColor: "text-blue-600 dark:text-blue-400"
    }
  ];

  /**
   * Función de renderizado condicional del contenido principal
   * @function renderContent
   * @param {boolean} [darkMode=false] - Indica si el modo oscuro está activo
   * @returns {JSX.Element} Componente correspondiente a la sección activa
   * @description Renderiza el contenido apropiado según la sección seleccionada
   * en la navegación. Cada sección tiene su propio componente especializado
   * con props específicas para su funcionalidad.
   *
   * @example
   * // Renderizar contenido del dashboard
   * const content = renderContent(true); // con modo oscuro
   */
  const renderContent = (darkMode = false) => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <>
            <h1 className="text-3xl font-light tracking-wide leading-relaxed mb-4">
              {t('admin.welcome')}
            </h1>
            <p className="text-gray-600 dark:text-white mb-6 font-light tracking-wide">
              {t('admin.subtitle')}
            </p>

            <DashboardStats
              mainStats={mainStats}
              miniStats={miniStats}
              isLoading={statsLoading}
              error={statsError}
              onClearError={clearStatsError}
            />
          </>
        );

      case 'users':
        return (
          <UserManagement
            users={users}
            isLoading={usersLoading}
            error={usersError}
            onClearError={clearError}
            onUpdateUser={handleUpdateUser}
            onCreateUser={handleCreateUser}
            onDeleteUser={handleDeleteUser}
            darkMode={darkMode}
          />
        );

      case 'keys':
        return (
          <AdminKeysSection
            usersWithKeys={usersWithKeys}
            isLoading={statsLoading}
            darkMode={darkMode}
          />
        );

      case 'profile':
        return (
          <AdminProfileManagement
            currentUser={currentUser}
            isLoading={usersLoading}
            error={usersError}
            onClearError={clearError}
            onUpdateProfile={handleUpdateProfile}
            onChangePassword={handleChangePassword}
            darkMode={darkMode}
          />
        );

      default:
        return null;
    }
  };

  // Componente wrapper que recibe darkMode del DashboardLayout
  const ContentWrapper = ({ darkMode }) => {
    // Capturar el estado de darkMode para usarlo en el modal
    if (darkMode !== currentDarkMode) {
      setCurrentDarkMode(darkMode);
    }
    return renderContent(darkMode);
  };

  return (
    <>
      <DashboardLayout
        title="Quantum Admin"
        currentUser={currentUser}
        navigationItems={navigationItems}
        activeSection={activeSection}
        onSectionChange={setActiveSection}
        onLogout={handleLogout}
        expectedRole={['admin', 'ADMIN']}
      >
        <ContentWrapper />
      </DashboardLayout>

      {/* Modal de éxito para cambio de contraseña */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title={t('dashboard.messages.passwordUpdated')}
        message={t('dashboard.messages.passwordUpdatedMessage')}
        autoCloseDelay={3000}
        darkMode={currentDarkMode}
      />
    </>
  );
};

export default AdminDashboard;
