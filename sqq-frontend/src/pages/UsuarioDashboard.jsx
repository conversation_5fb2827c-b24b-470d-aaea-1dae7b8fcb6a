import { useState, useEffect } from "react";
import {
  Key,
  User,
  BarChart3,
  Calendar,
  Shield,
} from "lucide-react";
import { useAuth, useKeys, useUsers, useLanguage } from '../hooks/index';
import { DashboardLayout, DashboardStats } from '../components/dashboard';
import { KeyManagement, ProfileManagement } from '../components/dashboard/user';
import SimpleBarChart from '../components/dashboard/user/SimpleBarChart';
import SimpleDoughnutChart from '../components/dashboard/user/SimpleDoughnutChart';
import SimpleRadar<PERSON>hart from '../components/dashboard/user/SimpleRadarChart';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '../components/dashboard/user/SimpleLineChart';
import authService from '../services/auth/authService';
import SuccessModal from '../components/common/SuccessModal';
import { ErrorModal } from '../components/common';
import { getDisplayLastAccess, formatLastAccess, setLastAccess } from '../utils/LastAccessTracker';

const UsuarioDashboard = () => {
  const [activeSection, setActiveSection] = useState('dashboard');
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showKeySuccessModal, setShowKeySuccessModal] = useState(false);
  const [showKeyErrorModal, setShowKeyErrorModal] = useState(false);
  const [showUpdateSuccessModal, setShowUpdateSuccessModal] = useState(false);
  const [uploadedKeyInfo, setUploadedKeyInfo] = useState(null);
  const [keyErrorInfo, setKeyErrorInfo] = useState(null);
  const [updatedKeyInfo, setUpdatedKeyInfo] = useState(null);
  const [currentDarkMode, setCurrentDarkMode] = useState(false);

  // Hooks para autenticación, llaves y usuarios
  const { user: currentUser, logout } = useAuth();
  const { t, currentLanguage } = useLanguage();
  const {
    keys: userKeys,
    statistics,
    isLoading: keysLoading,
    error: keysError,
    uploadToCTM,
    getKeysByUser,
    deleteKey,
    updateKey,
    getKeyVersions,
    clearError
  } = useKeys();
  const {
    updateUser,
    isLoading: userLoading,
    error: userError,
    clearError: clearUserError
  } = useUsers();

  // Cargar llaves del usuario al montar el componente
  useEffect(() => {
    const loadUserKeys = async () => {
      if (currentUser && currentUser.id) {
        try {
          await getKeysByUser(currentUser.id);
        } catch {
          // Error loading user keys - handled by hooks
        }
      }
    };

    loadUserKeys();
  }, [currentUser, getKeysByUser]);

  // Guardar fecha de acceso actual
  useEffect(() => {
    if (currentUser) {
      setLastAccess();
    }
  }, [currentUser]);

  // Manejar logout
  const handleLogout = () => {
    logout();
    
    // Limpiar storage
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('userRole');
    sessionStorage.removeItem('tokenExpiry');
    sessionStorage.removeItem('loginTime');
    localStorage.removeItem('token');
    localStorage.removeItem('userRole');
    sessionStorage.setItem('wasLoggedOut', 'true');
    
    window.location.href = '/';
  };

  // Funciones para manejar llaves
  const handleUploadKey = async (keyData) => {
    try {
      const result = await uploadToCTM(keyData);
      
      // Guardar información de la llave subida para el modal de éxito
      setUploadedKeyInfo({
        name: keyData.key_name,
        algorithm: keyData.algorithm,
        size: keyData.num_bytes,
        id: result.ctmKeyId || result.id
      });
      
      // Mostrar modal de éxito específico para llaves
      setShowKeySuccessModal(true);
      
      // Recargar la lista de llaves después de subir exitosamente
      if (currentUser && currentUser.id) {
        await getKeysByUser(currentUser.id);
      }
      return result;
    } catch (error) {
      // Guardar información del error para el modal de error
      setKeyErrorInfo({
        name: keyData.key_name,
        algorithm: keyData.algorithm,
        size: keyData.num_bytes,
        errorMessage: error.message || t('common.unknownError')
      });
      
      // Mostrar modal de error específico para llaves
      setShowKeyErrorModal(true);
      
      // Re-lanzar el error para que el modal de subida pueda manejarlo
      throw error;
    }
  };

  const handleDeleteKey = async (keyId) => {
    await deleteKey(keyId);
  };

  const handleUpdateKey = async (keyId) => {
    try {
      // Buscar la llave para obtener su ctmKeyId
      const key = userKeys.find(k => k.id === keyId);
      if (!key || !key.ctmKeyId) {
        throw new Error(t('common.ctmKeyNotFound'));
      }
      
      const response = await updateKey(key.ctmKeyId);
      
      // Mostrar modal de éxito
      setUpdatedKeyInfo({
        name: key.name || 'Llave',
        timestamp: new Date().toISOString()
      });
      setShowUpdateSuccessModal(true);
      
      // Recargar la lista de llaves después de actualizar exitosamente
      if (currentUser && currentUser.id) {
        await getKeysByUser(currentUser.id);
      }
      
      return response;
    } catch (error) {
      console.error('Error updating key:', error);
      throw error;
    }
  };

  // Función para actualizar perfil
  const handleUpdateProfile = async (profileData) => {
    await updateUser(currentUser.id, profileData);
    // Los cambios se reflejan automáticamente sin necesidad de recargar
  };

  // Función para cambiar contraseña
  const handleChangePassword = async (passwordData) => {
    try {
      // Llamada real al servicio de cambio de contraseña
      await authService.changePassword(
        passwordData.currentPassword,
        passwordData.newPassword,
        passwordData.confirmPassword
      );

      // Mostrar modal de éxito elegante
      setShowSuccessModal(true);
    } catch (error) {
      console.error('Error al cambiar contraseña:', error);
      throw new Error(error.message || t('common.passwordChangeError'));
    }
  };

  // Función para manejar clicks en las cards de estadísticas
  const handleStatClick = (stat) => {
    if (stat.title === t('dashboard.stats.myKeys') || stat.title === t('dashboard.stats.activeKeys')) {
      setActiveSection('keys');
    }
  };

  // Configuración de navegación
  const navigationItems = [
    { key: 'dashboard', label: t('navigation.dashboard'), icon: BarChart3 },
    { key: 'keys', label: t('navigation.keys'), icon: Key },
    { key: 'profile', label: t('navigation.profile'), icon: User }
  ];

  // Configuración de estadísticas principales
  const mainStats = [
    {
      icon: Key,
      title: t('dashboard.stats.myKeys'),
      value: statistics?.total || userKeys.length,
      description: t('dashboard.stats.keysGenerated'),
      iconColor: "text-blue-500",
      valueColor: "text-blue-600 dark:text-blue-400",
      percentage: 75,
      clickable: true
    },
    {
      icon: Shield,
      title: t('dashboard.stats.activeKeys'),
      value: statistics?.uploadedToCtm || userKeys.filter(key =>
        key.status === 'UPLOADED_TO_CTM' || key.uploadedToCtm === true
      ).length,
      description: t('dashboard.stats.keysActive'),
      iconColor: "text-green-500",
      valueColor: "text-green-600 dark:text-green-400",
      percentage: 75,
      clickable: true
    },
    {
      icon: Calendar,
      title: t('dashboard.stats.lastAccess'),
      value: formatLastAccess(getDisplayLastAccess(), currentLanguage === 'es' ? 'es-ES' : 'en-US'),
      description: t('dashboard.stats.lastLoginDate'),
      iconColor: "text-purple-500",
      valueColor: "text-purple-600 dark:text-purple-400",
      percentage: null, // Sin porcentaje circular
      clickable: false
    }
  ];

  // Configuración de estadísticas adicionales
  const miniStats = statistics ? [
    {
      value: statistics.successful,
      label: t('dashboard.miniStats.successful'),
      valueColor: "text-green-600 dark:text-green-400"
    },
    {
      value: statistics.failed,
      label: t('dashboard.miniStats.failed'),
      valueColor: "text-red-600 dark:text-red-400"
    },
    {
      value: statistics.uploadedToCtm,
      label: t('dashboard.miniStats.inCTM'),
      valueColor: "text-blue-600 dark:text-blue-400"
    },
    {
      value: (() => {
        if (!statistics.total || statistics.total === 0) return 0;
        const percentage = Math.round((statistics.successful / statistics.total) * 100);
        return Math.min(percentage, 100); // Asegurar que no exceda 100%
      })(),
      label: t('dashboard.miniStats.successRate'),
      valueColor: "text-purple-600 dark:text-purple-400"
    }
  ] : [];

  const renderContent = (darkMode = false) => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <>
            <div className="text-center sm:text-left mb-6 sm:mb-8">
              <h1 className="text-2xl sm:text-3xl font-light tracking-wide leading-relaxed mb-2 sm:mb-4 text-gray-900 dark:text-white">
                {t('dashboard.welcome', { name: currentUser ? `${currentUser.firstName} ${currentUser.lastName}` : t('common.user') })}
              </h1>
              <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 font-light tracking-wide">
                {t('dashboard.subtitle')}
              </p>
            </div>

            <DashboardStats
              mainStats={mainStats}
              miniStats={miniStats}
              isLoading={keysLoading}
              error={keysError}
              onClearError={clearError}
              onStatClick={handleStatClick}
            />

            {/* Gráficos con datos reales */}
            <div className="space-y-6 mt-8">
              {/* Primera fila: Barras y Dona */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <SimpleBarChart darkMode={currentDarkMode} keys={userKeys} />
                <SimpleDoughnutChart darkMode={currentDarkMode} statistics={statistics} />
              </div>
              
              {/* Segunda fila: Radar y Líneas */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <SimpleRadarChart darkMode={currentDarkMode} keys={userKeys} />
                <SimpleLineChart darkMode={currentDarkMode} keys={userKeys} />
              </div>
            </div>


          </>
        );

      case 'keys':
        return (
          <KeyManagement
            keys={userKeys}
            isLoading={keysLoading}
            error={keysError}
            onClearError={clearError}
            onUploadKey={handleUploadKey}
            onDeleteKey={handleDeleteKey}
            onUpdateKey={handleUpdateKey}
            getKeyVersions={getKeyVersions}
            darkMode={darkMode}
          />
        );

      case 'profile':
        return (
          <ProfileManagement
            currentUser={currentUser}
            isLoading={userLoading}
            error={userError}
            onClearError={clearUserError}
            onUpdateProfile={handleUpdateProfile}
            onChangePassword={handleChangePassword}
            darkMode={darkMode}
          />
        );
        
      default:
        return null;
    }
  };

  // Componente wrapper que recibe darkMode del DashboardLayout
  const ContentWrapper = ({ darkMode }) => {
    // Capturar el estado de darkMode para usarlo en el modal
    if (darkMode !== currentDarkMode) {
      setCurrentDarkMode(darkMode);
    }
    return renderContent(darkMode);
  };

  return (
    <>
      <DashboardLayout
        title="Quantum Usuario"
        currentUser={currentUser}
        navigationItems={navigationItems}
        activeSection={activeSection}
        onSectionChange={setActiveSection}
        onLogout={handleLogout}
        expectedRole={['usuario', 'USER', 'user']}
      >
        <ContentWrapper />
      </DashboardLayout>

      {/* Modal de éxito para cambio de contraseña */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title={t('dashboard.messages.passwordUpdated')}
        message={t('dashboard.messages.passwordUpdatedMessage')}
        autoCloseDelay={3000}
        darkMode={currentDarkMode}
      />

      {/* Modal de éxito para subida de llaves */}
      <SuccessModal
        isOpen={showKeySuccessModal}
        onClose={() => {
          setShowKeySuccessModal(false);
          setUploadedKeyInfo(null);
        }}
        title={t('keys.modals.success.title')}
        message={uploadedKeyInfo ? 
          t('keys.modals.success.message', {
            name: uploadedKeyInfo.name,
            algorithm: uploadedKeyInfo.algorithm,
            size: uploadedKeyInfo.size
          })
          : t('keys.modals.success.messageGeneric')
        }
        autoCloseDelay={4500}
        darkMode={currentDarkMode}
      />

      {/* Modal de error para subida de llaves */}
      <ErrorModal
        isOpen={showKeyErrorModal}
        onClose={() => {
          setShowKeyErrorModal(false);
          setKeyErrorInfo(null);
        }}
        title={t('keys.modals.error.title')}
        message={keyErrorInfo ? 
          t('keys.modals.error.message', {
            name: keyErrorInfo.name,
            algorithm: keyErrorInfo.algorithm,
            size: keyErrorInfo.size,
            error: keyErrorInfo.errorMessage
          })
          : t('keys.modals.error.messageGeneric')
        }
        autoCloseDelay={0} // No auto-close para errores
        darkMode={currentDarkMode}
      />

      {/* Modal de éxito para actualización de llaves */}
      <SuccessModal
        isOpen={showUpdateSuccessModal}
        onClose={() => {
          setShowUpdateSuccessModal(false);
          setUpdatedKeyInfo(null);
        }}
        title={t('keys.update.success.title')}
        message={updatedKeyInfo ? 
          t('keys.update.success.message', { name: updatedKeyInfo.name })
          : t('keys.update.success.message', { name: 'Llave' })
        }
        autoCloseDelay={4000}
        darkMode={currentDarkMode}
      />
    </>
  );
};

export default UsuarioDashboard;
